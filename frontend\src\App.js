import { useState } from "react";
import { Routes, Route, Navigate } from "react-router-dom";

import Topbar from "./pages/global/Topbar";
import Sidebar from "./pages/global/Sidebar";
import Dashboard from "./pages/DashboardPage";
import Login from "./pages/LoginPage";
import ResetPasswordPage from "./pages/ResetPasswordPage";
import InspectionFormPage from "./pages/InspectionFormPage";
import Documents from "./pages/Documents";
import ReferenceDocumentsPage from "./pages/ReferenceDocumentsPage";

function App() {
  const [isSidebar, setIsSidebar] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false); // Always start as false to require login

  return (
    <div className="app">
      {isAuthenticated ? (
        <>
          <Sidebar isSidebar={isSidebar} />
          <main className="content">
            <Topbar setIsSidebar={setIsSidebar} />
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/InspectionFormPage" element={<InspectionFormPage/>} />
              <Route path="/documents" element={<Documents />} />
              <Route path="/reference-documents" element={<ReferenceDocumentsPage />} />
              <Route path="/login" element={<Navigate to="/" />} />
              {/* Add other routes here as needed */}
            </Routes>
          </main>
        </>
      ) : (
        <Routes>
          <Route path="/login" element={<Login setIsAuthenticated={setIsAuthenticated} />} />
          <Route path="/reset-password" element={<ResetPasswordPage />} />
          <Route path="/" element={<Login setIsAuthenticated={setIsAuthenticated} />} />
          <Route path="*" element={<Navigate to="/login" />} />
        </Routes>
      )}
    </div>
  );
}

export default App;